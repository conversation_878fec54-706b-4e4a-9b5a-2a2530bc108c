package fr.enedis.i2r.si.mock;

import static fr.enedis.i2r.comsi.rest.HttpsServer.SECURE_PORT;
import static fr.enedis.i2r.comsi.rest.RestEndpoints.DB_CFG;
import static fr.enedis.i2r.comsi.rest.RestEndpoints.STATUS_CHANGE;

import java.io.IOException;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.infra.rest.SiHeaders;
import io.javalin.http.Context;
import io.javalin.openapi.HttpMethod;
import io.javalin.openapi.OpenApi;

public class BipCaller {
    private static final Logger logger = LoggerFactory.getLogger(BipCaller.class);

    private final HttpClient client;

    public BipCaller(HttpClient client) {
        this.client = client;
    }

    public void setStableStatus(Context ctx) throws IOException, InterruptedException {
        String configHash = Optional.ofNullable(ctx.header(SiHeaders.Constants.HASH_HEADER))
                .orElseThrow(() -> new IllegalArgumentException("Missing configuration hash header"));
        String idms = ctx.pathParam("idms");

        logger.info("Send setStableStatus");
        logger.info("Hash : {}", configHash);
        logger.info("IDMS : {}", idms);
        logger.info("");

        String targetUrl = buildBipUrl(STATUS_CHANGE);
        HttpRequest request = HttpRequestBuilder.create(targetUrl)
                .withApiVersion()
                .withIdms(idms)
                .withHash(configHash)
                .withPlainTextContentType()
                .buildPut(Constants.STABLE_STATUS_PAYLOAD);

        HttpResponse<String> response = sendRequest(request);
        logResponse(response);
    }

    @OpenApi(
        summary = "Demande la config du boitier",
        operationId = "askConfiguration",
        path = "/trigger-cfg-request",
        methods = HttpMethod.GET,
        tags = { "Back-end" }
    )
    public void requestFullCfg(Context ctx) throws IOException, InterruptedException {
        logger.info("Send requestFullCfg");
        logger.info("");

        String targetUrl = buildBipUrl(DB_CFG);
        HttpRequest request = HttpRequestBuilder.create(targetUrl)
                .withApiVersion()
                .withIdms(Constants.TEST_IDMS)
                .buildGet();

        HttpResponse<String> response = sendRequest(request);
        logResponse(response);
    }

    private String buildBipUrl(String endpoint) {
        return "https://" + "127.0.0.1" + ":" + SECURE_PORT + endpoint;
    }

    private HttpResponse<String> sendRequest(HttpRequest request) throws IOException, InterruptedException {
        return client.send(request, BodyHandlers.ofString());
    }

    private void logResponse(HttpResponse<String> response) {
        logger.info("Status : {}", response.statusCode());
        logger.info("Contenu : {}", response.body());
        logger.info("Content-Type : {}", response.headers().firstValue("Content-Type").orElse("inconnu"));
    }
}
