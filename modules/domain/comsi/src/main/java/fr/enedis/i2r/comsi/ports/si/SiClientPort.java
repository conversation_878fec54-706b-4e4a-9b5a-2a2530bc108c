package fr.enedis.i2r.comsi.ports.si;

import java.util.List;

import fr.enedis.i2r.comsi.ConfigurationBoitier;
import fr.enedis.i2r.comsi.errors.RequestToSiException;
import fr.enedis.i2r.comsi.metrics.RawJsonMetric;

public interface SiClientPort {
    void sendConfigurationBoitier(ConfigurationBoitier config) throws RequestToSiException;
    void sendMetricsBoitier(List<RawJsonMetric> rawJsonMetrics) throws RequestToSiException;

    SiConfigurationNotifierPort getSiConfigurationNotifier();
}
