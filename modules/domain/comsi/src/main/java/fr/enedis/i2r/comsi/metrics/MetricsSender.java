package fr.enedis.i2r.comsi.metrics;
import static java.time.temporal.ChronoUnit.SECONDS;

import java.time.Duration;
import java.time.OffsetTime;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.errors.RequestToSiException;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;

public class MetricsSender implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(MetricsSender.class);
    public static final String METRICS_JSONL_PATH = "/var/lib/i2r/metrics/metrics.jsonl";

    private ComSiConfiguration configuration;
    private SiClientPort siClientPort;
    private MetricsFileReader metricsFileReader;
    private ScheduledFuture<?> collectMetricsTaskFuture;
    private final ScheduledExecutorService scheduler;

    public MetricsSender(ComSiConfiguration comSiConfiguration, SiClientPort siClientPort) {
        this.configuration = comSiConfiguration;
        this.siClientPort = siClientPort;
        this.metricsFileReader = new MetricsFileReader(METRICS_JSONL_PATH);
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "MetricsSender");
            t.setDaemon(true);
            return t;
        });

        this.collectMetricsTaskFuture = scheduler.schedule(() -> {}, 0, TimeUnit.MILLISECONDS);
        this.collectMetricsTaskFuture.cancel(false);
    }

    @Override
    public void run() {
        if (collectMetricsTaskFuture.isCancelled() || collectMetricsTaskFuture.isDone()) {
            collectMetricsTaskFuture = scheduler.scheduleAtFixedRate(
                    () -> {
                        try {
                            var metrics = metricsFileReader.getMetrics();
                            this.siClientPort.sendMetricsBoitier(metrics);
                        } catch (MetricsException e) {
                            logger.error(e.getMessage(),e);
                        } catch (RequestToSiException e) {
                            logger.error(e.getMessage(),e);
                        }
                    },
                    getInitialDelaySeconds(configuration.metricsSendingTime()),
                    TimeUnit.DAYS.toSeconds(1),
                    TimeUnit.SECONDS
            );
        }
    }

    private long getInitialDelaySeconds(OffsetTime targetTime) {
        OffsetTime now = OffsetTime.now();
        long reportDemain = 0L;
        if (now.isAfter(targetTime)) {
            reportDemain = Duration.ofDays(1).toSeconds();
        }
        return now.until(targetTime, SECONDS) + reportDemain;
    }
}
