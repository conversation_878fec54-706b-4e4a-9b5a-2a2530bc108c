package fr.enedis.i2r.infra.rest.si.ao2.metrics;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.HashMap;
import java.util.Map;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.metrics.RawJsonMetric;

class StatCellServingTest {

    @Test
    void la_fonction_from_genere_un_objet_correct() {
        // Arrange
        Map<String, String> fields = new HashMap<>();
        fields.put("earfcn", "123");
        fields.put("mcc", "208");
        fields.put("mnc", "10");
        fields.put("pci", "456");
        fields.put("rat", "LTE");
        fields.put("rsrp", "-95");
        fields.put("rsrq", "-10");
        fields.put("rssi", "-70");
        fields.put("sinr", "20");
        fields.put("tac", "789");

        long timestamp = 1_600_000_000_000L; // fixed timestamp for reproducibility

        RawJsonMetric rawMetric = new RawJsonMetric(fields, "serving-cell", Map.of(), timestamp);

        // Act
        StatCellServing stat = StatCellServing.from(rawMetric);

        // Assert
        assertEquals(123, stat.getEarfcn());
        assertEquals(208, stat.getMcc());
        assertEquals(10, stat.getMnc());
        assertEquals(456, stat.getPci());
        assertEquals("LTE", stat.getTechno());
        assertEquals(-95, stat.getRsrp());
        assertEquals(-10, stat.getRsrq());
        assertEquals(-70, stat.getRssi());
        assertEquals(20, stat.getSinr());
        assertEquals(789, stat.getTac());

        assertNotNull(stat.getDateMes());
        assertThat(stat.getDateMes()).isNotEmpty();

        // Inherited Base defaults
        assertEquals("StatCellServing", stat.getClazz());
        assertEquals("StatCellServing", stat.getName());
        assertEquals("LOGDM", stat.getEntity());
        assertTrue(stat.getPub());
    }
}
