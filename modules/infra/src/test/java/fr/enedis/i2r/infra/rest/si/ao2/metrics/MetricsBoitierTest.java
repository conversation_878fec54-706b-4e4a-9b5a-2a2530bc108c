package fr.enedis.i2r.infra.rest.si.ao2.metrics;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.metrics.RawJsonMetric;

class MetricsBoitierTest {

    @Test
    void les_metrics_json_serving_cell_sont_ajoutees() {

        Map<String, String> fields1 = new HashMap<>();
        fields1.put("cellid", "25167362");
        fields1.put("earfcn", "1300");
        fields1.put("mcc", "208");
        fields1.put("mnc", "1");
        fields1.put("pci", "334");
        fields1.put("rat", "eMTC");
        fields1.put("rsrp", "-81");
        fields1.put("rsrq", "-7");
        fields1.put("rssi", "-58");
        fields1.put("sinr", "-16");
        fields1.put("tac", "51908");
        RawJsonMetric metric1 = new RawJsonMetric(fields1, "serving-cell", Map.of(), 1756296811L);

        Map<String, String> fields2 = new HashMap<>();
        fields2.put("cellid", "25167362");
        fields2.put("earfcn", "1300");
        fields2.put("mcc", "208");
        fields2.put("mnc", "1");
        fields2.put("pci", "334");
        fields2.put("rat", "eMTC");
        fields2.put("rsrp", "-85");
        fields2.put("rsrq", "-3");
        fields2.put("rssi", "-68");
        fields2.put("sinr", "-17");
        fields2.put("tac", "51908");
        RawJsonMetric metric2 = new RawJsonMetric(fields2, "serving-cell", Map.of(), 1756296828L);

        List<RawJsonMetric> rawJsonMetrics = List.of(metric1, metric2);

        // Act
        MetricsBoitier metricsBoitier = MetricsBoitier.from(rawJsonMetrics);

        // Assert
        assertThat(metricsBoitier).isNotNull();
        assertThat(metricsBoitier.getNb()).isEqualTo(2);
        assertThat(metricsBoitier.getObjects())
                .hasSize(2)
                .allMatch(obj -> obj instanceof StatCellServing);
    }

    @Test
    void les_metrics_json_non_supportees_sont_ignorees() {

        // Add a metric with unsupported name (should be ignored)
        RawJsonMetric unsupportedMetric = new RawJsonMetric(Map.of(), "NON-SUPPORTED", Map.of(), 123456L);
        List<RawJsonMetric> rawJsonMetrics = List.of(unsupportedMetric);

        // Act
        MetricsBoitier metricsBoitier = MetricsBoitier.from(rawJsonMetrics);

        // Assert
        assertThat(metricsBoitier).isNotNull();
        assertThat(metricsBoitier.getObjects().isEmpty());
        assertThat(metricsBoitier.getNb()).isEqualTo(0);
    }
}
