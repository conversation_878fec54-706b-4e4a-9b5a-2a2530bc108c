package fr.enedis.i2r.infra.rest;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

import com.fasterxml.jackson.core.JsonProcessingException;

import fr.enedis.i2r.comsi.ConfigurationBoitier;
import fr.enedis.i2r.comsi.errors.RequestToSiException;
import fr.enedis.i2r.comsi.metrics.RawJsonMetric;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.infra.rest.si.ao2.Base;
import fr.enedis.i2r.infra.rest.si.ao2.ConfigurationBoitierIcare;
import fr.enedis.i2r.infra.rest.si.ao2.metrics.MetricsBoitier;
import fr.enedis.i2r.infra.rest.si.ao2.metrics.StatCellServing;
import fr.enedis.i2r.infra.rest.si.error.IComBadRequestException;
import io.javalin.http.HttpStatus;

public class SiClientHttpAdapterTest {
    HttpClient httpClient;
    BoardManagerPort boardManagerPort;
    ComsiParametersPort parametersPort;

    @BeforeEach
    void setup() {
        httpClient = mock(HttpClient.class);
        boardManagerPort = mock(BoardManagerPort.class);
        parametersPort = mock(ComsiParametersPort.class);
    }

    @Test
    void le_client_envoie_la_configuration_boitier_au_datacenter_primaire() throws RequestToSiException, IComBadRequestException {
        CustomComSiConfiguration conf = new CustomComSiConfiguration();

        var successfulResponse = new HttpResponse();
        successfulResponse.setStatus(HttpStatus.OK);

        ArgumentCaptor<HttpRequest> captor = ArgumentCaptor.forClass(HttpRequest.class);

        when(httpClient.retryRequest(captor.capture())).thenReturn(successfulResponse);

        SiClientHttpAdapter siClient = new SiClientHttpAdapter(conf.build(), httpClient, parametersPort, "ADS", "IDMS");
        siClient.sendConfigurationBoitier(
                ConfigurationBoitier.from(conf.build(), Instant.now(), "HASH", "ADS", "IDMS", "ICCID"));

        HttpRequest requeteEnvoyee = captor.getValue();
        assertThat(requeteEnvoyee.uri().getHost()).isEqualTo(conf.ipPacy.ip());
    }

    @Test
    void la_configuration_est_envoyee_au_datacenter_secondaire_lorsque_l_envoi_echoue_sur_le_primaire() throws RequestToSiException, IComBadRequestException {
        CustomComSiConfiguration conf = new CustomComSiConfiguration();

        var notFoundResponse = new HttpResponse();
        notFoundResponse.setStatus(HttpStatus.BAD_REQUEST);

        ArgumentCaptor<HttpRequest> captor = ArgumentCaptor.forClass(HttpRequest.class);

        when(httpClient.retryRequest(captor.capture())).thenThrow(new IComBadRequestException(500, "erreur"));

        SiClientHttpAdapter siClient = new SiClientHttpAdapter(conf.build(), httpClient, parametersPort, "ADS", "IDMS");

        assertThatExceptionOfType(RequestToSiException.class).isThrownBy(() ->
            siClient.sendConfigurationBoitier(ConfigurationBoitier.from(conf.build(), Instant.now(), "HASH", "ADS", "IDMS", "ICCID"))
        );

        List<HttpRequest> requetesEnvoyees = captor.getAllValues();
        assertThat(requetesEnvoyees).hasSize(2);
        assertThat(requetesEnvoyees.get(0).uri().getHost()).isEqualTo(conf.ipPacy.toString());
        assertThat(requetesEnvoyees.get(1).uri().getHost()).isEqualTo(conf.ipNoe.toString());
    }

    @Test
    void la_configuration_boitier_est_correctement_serialisee_pour_icare() throws RequestToSiException, IComBadRequestException {
        var conf = new CustomComSiConfiguration().build();

        var now = Instant.now();
        var confBoitier = ConfigurationBoitier.from(conf, now, "HASH", "ADS", "IDMS", "ICCID");

        var successfulResponse = new HttpResponse();
        successfulResponse.setStatus(HttpStatus.OK);

        ArgumentCaptor<HttpRequest> captor = ArgumentCaptor.forClass(HttpRequest.class);

        when(httpClient.retryRequest(captor.capture())).thenReturn(successfulResponse);

        SiClientHttpAdapter siClient = new SiClientHttpAdapter(conf, httpClient, parametersPort, "ADS", "IDMS");
        siClient.sendConfigurationBoitier(confBoitier);

        HttpRequest requeteEnvoyee = captor.getValue();
        assertThat(requeteEnvoyee.uri().getHost()).isEqualTo(conf.parseDatacenterConfiguration().primaryIp().ip());
        assertThat(requeteEnvoyee.payload()).isInstanceOf(ConfigurationBoitierIcare.class);

        ConfigurationBoitierIcare sentConfiguration = (ConfigurationBoitierIcare) requeteEnvoyee.payload();
        assertThat(sentConfiguration.getNb()).isEqualTo(3);
        // TODO: tester plus de champs
    }


    @Test
    void l_envoi_de_metrics_envoie_le_bon_objet()  throws RequestToSiException, IComBadRequestException, JsonProcessingException {
        var conf = new CustomComSiConfiguration().build();
        var successfulResponse = new HttpResponse();
        successfulResponse.setStatus(HttpStatus.OK);

        ArgumentCaptor<HttpRequest> captor = ArgumentCaptor.forClass(HttpRequest.class);

        when(httpClient.retryRequest(captor.capture())).thenReturn(successfulResponse);

        SiClientHttpAdapter siClient = new SiClientHttpAdapter(conf, httpClient, parametersPort, "ADS", "IDMS");

        List<RawJsonMetric> rawJsonMetrics = generateRawJsonMetric();

        siClient.sendMetricsBoitier(rawJsonMetrics);

        HttpRequest requeteEnvoyee = captor.getValue();
        assertThat(requeteEnvoyee.payload()).isInstanceOf(MetricsBoitier.class);
    }

    @Test
    void le_parsing_et_l_envoi_de_rawjsonmetrics_est_fonctionnel()  throws RequestToSiException, IComBadRequestException, JsonProcessingException {
        var conf = new CustomComSiConfiguration().build();
        var successfulResponse = new HttpResponse();
        successfulResponse.setStatus(HttpStatus.OK);

        ArgumentCaptor<HttpRequest> captor = ArgumentCaptor.forClass(HttpRequest.class);

        when(httpClient.retryRequest(captor.capture())).thenReturn(successfulResponse);

        SiClientHttpAdapter siClient = new SiClientHttpAdapter(conf, httpClient, parametersPort, "ADS", "IDMS");

        // Init variables used for testing
        List<RawJsonMetric> rawJsonMetrics = generateRawJsonMetric();

        // Manually set items that are expected to be in the sent metrics
        // First expected metric
        StatCellServing metricCellServing1 = new StatCellServing();
        metricCellServing1.setEarfcn(1300);
        metricCellServing1.setMcc(208);
        metricCellServing1.setMnc(1);
        metricCellServing1.setPci(334);
        metricCellServing1.setTechno("eMTC");
        metricCellServing1.setRsrp(-81);
        metricCellServing1.setRsrq(-7);
        metricCellServing1.setRssi(-58);
        metricCellServing1.setSinr(-16);
        metricCellServing1.setTac(51908);
        metricCellServing1.setDateMes(Instant.ofEpochSecond(1756296811L).atZone(ZoneId.systemDefault()).format(Base.SI_DATE_FORMATTER));

        // Second expected metric
        StatCellServing metricCellServing2 = new StatCellServing();
        metricCellServing2.setEarfcn(1300);
        metricCellServing2.setMcc(208);
        metricCellServing2.setMnc(1);
        metricCellServing2.setPci(334);
        metricCellServing2.setTechno("eMTC");
        metricCellServing2.setRsrp(-85);
        metricCellServing2.setRsrq(-3);
        metricCellServing2.setRssi(-68);
        metricCellServing2.setSinr(-17);
        metricCellServing2.setTac(51908);
        metricCellServing2.setDateMes(Instant.ofEpochSecond(1756296828L).atZone(ZoneId.systemDefault()).format(Base.SI_DATE_FORMATTER));

        siClient.sendMetricsBoitier(rawJsonMetrics);

        HttpRequest requeteEnvoyee = captor.getValue();

        MetricsBoitier sentMetrics = (MetricsBoitier) requeteEnvoyee.payload();

        // Verify that the objects list of sentMetrics contains the objects metricCellServing1 and metricCellServing2
        assertThat(sentMetrics.getObjects().stream().anyMatch(metric -> customStatCellServingEquals((StatCellServing) metric, (StatCellServing) metricCellServing1))).isTrue();
        assertThat(sentMetrics.getObjects().stream().anyMatch(metric -> customStatCellServingEquals((StatCellServing) metric, (StatCellServing) metricCellServing2))).isTrue();

    }

    /** Méthode pour comparer deux StateCellServings, verifie et compare chaque champ sauf le champ "modified" (qui a pour valeur la date de l'instanciation de l'objet) */
    static boolean customStatCellServingEquals(StatCellServing o1, StatCellServing o2) {
        return
            o1.getDateMes().equals(o2.getDateMes()) &&
            o1.getEarfcn().equals(o2.getEarfcn()) &&
            o1.getMcc().equals(o2.getMcc()) &&
            o1.getMnc().equals(o2.getMnc()) &&
            o1.getPci().equals(o2.getPci()) &&
            o1.getTechno().equals(o2.getTechno()) &&
            o1.getRsrp().equals(o2.getRsrp()) &&
            o1.getRsrq().equals(o2.getRsrq()) &&
            o1.getRssi().equals(o2.getRssi()) &&
            o1.getSinr().equals(o2.getSinr()) &&
            o1.getTac().equals(o2.getTac());
    }

    static List<RawJsonMetric> generateRawJsonMetric() {

        List<RawJsonMetric> metrics = new ArrayList<RawJsonMetric>();

        // Premier objet (serving-cell)
        Map<String, String> fields1 = new HashMap<>();
        fields1.put("cellid", "25167362");
        fields1.put("earfcn", "1300");
        fields1.put("is_connected", "true");
        fields1.put("mcc", "208");
        fields1.put("mnc", "1");
        fields1.put("pci", "334");
        fields1.put("rat", "eMTC");
        fields1.put("rsrp", "-81");
        fields1.put("rsrq", "-7");
        fields1.put("rssi", "-58");
        fields1.put("sinr", "-16");
        fields1.put("tac", "51908");

        RawJsonMetric metric1 = new RawJsonMetric(
            fields1,
            "serving-cell",
            new HashMap<>(),
            1756296811L
        );

        // Deuxième objet (serving-cell)
        Map<String, String> fields2 = new HashMap<>();
        fields2.put("cellid", "25167362");
        fields2.put("earfcn", "1300");
        fields2.put("is_connected", "true");
        fields2.put("mcc", "208");
        fields2.put("mnc", "1");
        fields2.put("pci", "334");
        fields2.put("rat", "eMTC");
        fields2.put("rsrp", "-85");
        fields2.put("rsrq", "-3");
        fields2.put("rssi", "-68");
        fields2.put("sinr", "-17");
        fields2.put("tac", "51908");

        RawJsonMetric metric2 = new RawJsonMetric(
            fields2,
            "serving-cell",
            new HashMap<>(),
            1756296828L
        );

        // Troisième objet (Metrics avec un name non supporté)
        Map<String, String> fields3 = new HashMap<>();
        fields3.put("io_time", "5696");
        fields3.put("iops_in_progress", "0");
        fields3.put("merged_reads", "1732");
        fields3.put("merged_writes", "1056");
        fields3.put("read_bytes", "318915072");
        fields3.put("read_time", "8848");
        fields3.put("reads", "7353");
        fields3.put("weighted_io_time", "15589");
        fields3.put("write_bytes", "7979008");
        fields3.put("write_time", "6686");
        fields3.put("writes", "357");

        Map<String, String> tags3 = new HashMap<>();
        tags3.put("name", "mmcblk0");

        // Metric avec un name non pris en charge
        RawJsonMetric metric3 = new RawJsonMetric(
            fields3,
            "NOT-SUPPORTED-METRIC",
            tags3,
            1756296841L
        );


        metrics.add(metric1);
        metrics.add(metric2);
        metrics.add(metric3);

        return metrics;
    }

}
