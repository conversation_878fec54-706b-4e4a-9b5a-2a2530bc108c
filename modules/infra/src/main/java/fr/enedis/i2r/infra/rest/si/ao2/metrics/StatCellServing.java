package fr.enedis.i2r.infra.rest.si.ao2.metrics;

import java.time.Instant;
import java.time.ZoneId;

import fr.enedis.i2r.comsi.metrics.RawJsonMetric;
import fr.enedis.i2r.infra.rest.si.ao2.Base;

public class StatCellServing extends Base {

    /** Numéro de canal assigné */
    private Integer earfcn;

    /** Horodate de la mesure du signal */
    private String dateMes;

    /** Code pays du PLMN dans le plan GSM */
    private Integer mcc;

    /** Code réseau du PLMN dans le plan GSM */
    private Integer mnc;

    /** Puissance reçue du signal de référence en valeur absolue en dbm */
    private Integer rsrp;

    /** Qualité de réception du signal de référence */
    private Integer rsrq;

    /** Mesure de la puissance brute du signal radio reçu en large bande */
    private Integer rssi;

    /** Rapport entre le signal utile et les interférences plus le bruit. Un SINR élevé indique un signal clair avec peu d'interférences. */
    private Integer sinr;

    /** Identifiant unique d'une cellule  */
    private Integer pci;

    /** Code d’identification de la zone géographique au sein du PLMN */
    private Integer tac;

    /** Technologie utilisée: LTE, LTE-M, NB-IOT ... */
    private String techno;

    public StatCellServing() {

        // Hardcoded values extracted from sample stats sent to AO2 : See I2R-460 attachement
        this.setClazz("StatCellServing");
        this.setName("StatCellServing");
        this.setEntity("LOGDM");
        this.setPub(true);
    }

    public Integer getEarfcn() {
        return earfcn;
    }

    public void setEarfcn(Integer earfcn) {
        this.earfcn = earfcn;
    }

    public String getDateMes() {
        return dateMes;
    }

    public void setDateMes(String dateMes) {
        this.dateMes = dateMes;
    }

    public Integer getMcc() {
        return mcc;
    }

    public void setMcc(Integer mcc) {
        this.mcc = mcc;
    }

    public Integer getMnc() {
        return mnc;
    }

    public void setMnc(Integer mnc) {
        this.mnc = mnc;
    }

    public Integer getRsrp() {
        return rsrp;
    }

    public void setRsrp(Integer rsrp) {
        this.rsrp = rsrp;
    }

    public Integer getRsrq() {
        return rsrq;
    }

    public void setRsrq(Integer rsrq) {
        this.rsrq = rsrq;
    }

    public Integer getRssi() {
        return rssi;
    }

    public void setRssi(Integer rssi) {
        this.rssi = rssi;
    }

    public Integer getSinr() {
        return sinr;
    }

    public void setSinr(Integer sinr) {
        this.sinr = sinr;
    }

    public Integer getPci() {
        return pci;
    }

    public void setPci(Integer pci) {
        this.pci = pci;
    }

    public Integer getTac() {
        return tac;
    }

    public void setTac(Integer tac) {
        this.tac = tac;
    }

    public String getTechno() {
        return techno;
    }

    public void setTechno(String techno) {
        this.techno = techno;
    }

    public static StatCellServing from(RawJsonMetric rawMetric) {
        StatCellServing stateCellServing = new StatCellServing();

        stateCellServing.setEarfcn(Integer.valueOf(rawMetric.fields().get("earfcn")));
        stateCellServing.setMcc(Integer.valueOf(rawMetric.fields().get("mcc")));
        stateCellServing.setMnc(Integer.valueOf(rawMetric.fields().get("mnc")));
        stateCellServing.setPci(Integer.valueOf(rawMetric.fields().get("pci")));
        stateCellServing.setTechno(rawMetric.fields().get("rat"));
        stateCellServing.setRsrp(Integer.valueOf(rawMetric.fields().get("rsrp")));
        stateCellServing.setRsrq(Integer.valueOf(rawMetric.fields().get("rsrq")));
        stateCellServing.setRssi(Integer.valueOf(rawMetric.fields().get("rssi")));
        stateCellServing.setSinr(Integer.valueOf(rawMetric.fields().get("sinr")));
        stateCellServing.setTac(Integer.valueOf(rawMetric.fields().get("tac")));
        stateCellServing.setDateMes(Instant.ofEpochSecond(rawMetric.timestamp()).atZone(ZoneId.systemDefault()).format(SI_DATE_FORMATTER));
        stateCellServing.setEmitted(Instant.now().toString());
        stateCellServing.setFlow("/db/cfg/flows/stat");
        stateCellServing.setCmdKey("");
        return stateCellServing;
    }

}
