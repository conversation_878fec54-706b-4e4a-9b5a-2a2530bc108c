package fr.enedis.i2r.infra.rest;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import com.google.common.net.HttpHeaders;

public class SiHeaders {
    public interface Constants {
        // Enedis headers
        public static final String API_VERSION_HEADER = "X-Erdf-Api-Version";
        public static final String API_VERSION_VALUE = "2.0";
        public static final String IDMS_HEADER = "X-Idms";
        public static final String HASH_HEADER = "X-ERDF-HASH";
        //public final String MA_CORRELATION_ID = "maCorrelationId";
        public static final String CCMA_CORRELATION_ID = "ccma-correlation-id";
        public static final String MA_PROCESS_ID = "maProcessId";
        public static final String MA_SOURCE = "maSource";
        public static final String MA_DATE_EMISSION_FLUX = "maDateEmissionFlux";
        public static final String MA_VERSION = "maVersion";
        public static final String MA_ID_PRM = "maIdPrm";
        public static final String MA_ID_AFFAIRE = "maIdAffaire";
        public static final String MA_ID_EXTERNE = "maIdExterne";
        public static final String MA_ADS_BOITER = "maAdsBoitier";

        // Headers common values
        // TODO: These values are probably already defined somewhere in a library, use
        // those instead
        public static final String JSON_TYPE = "application/json";
        public static final String ZIP_FORMAT = "gzip";
    }

    public String ads;
    public String hash;

    public SiHeaders(String ads, String hash) {
        this.ads = ads;
        this.hash = hash;
    }

    public Map<String, String> toMap() {
        HashMap<String, String> headers = new HashMap<String, String>();

        headers.put(HttpHeaders.CONTENT_TYPE, SiHeaders.Constants.JSON_TYPE);
        headers.put(HttpHeaders.ACCEPT, SiHeaders.Constants.JSON_TYPE);
        headers.put(HttpHeaders.CONTENT_ENCODING, SiHeaders.Constants.ZIP_FORMAT);
        headers.put(HttpHeaders.ACCEPT_ENCODING, SiHeaders.Constants.ZIP_FORMAT);

        // TODO: Investigate what 2.0 is exactly
        headers.put(SiHeaders.Constants.API_VERSION_HEADER, SiHeaders.Constants.API_VERSION_VALUE);
        // l'identifiant DU MESSAGE de corrélation au format UUID
        headers.put(SiHeaders.Constants.CCMA_CORRELATION_ID, UUID.randomUUID().toString());
        // pour l'identifiant de processus SI (le n° de la CTD) >> Champ présent mais
        // non exploité aujourd'hui (05/11/2020)
        headers.put(SiHeaders.Constants.MA_PROCESS_ID, "CT0017");
        // pour l’identifiant de l'application qui émet l’événement (NNA)
        headers.put(SiHeaders.Constants.MA_SOURCE, "IBIS");
        // (ex : iDT vers iCoeur) pour ordonner le traitement des flux. Date au format
        // timestamp epoch
        headers.put(SiHeaders.Constants.MA_DATE_EMISSION_FLUX, String.valueOf(Instant.now().toEpochMilli()));
        // pour le suivi des versions du flux (qui peut être incrémenté pour prise en
        // compte de nouveaux besoins) identifiants fonctionnels:
        headers.put(SiHeaders.Constants.MA_VERSION, "1.0");
        headers.put(SiHeaders.Constants.MA_ADS_BOITER, ads);

        headers.put(SiHeaders.Constants.HASH_HEADER, hash);

        return headers;
    }
}
