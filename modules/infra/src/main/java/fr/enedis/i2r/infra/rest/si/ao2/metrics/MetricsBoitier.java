package fr.enedis.i2r.infra.rest.si.ao2.metrics;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.metrics.RawJsonMetric;
import fr.enedis.i2r.infra.rest.si.ao2.Base;
import fr.enedis.i2r.infra.rest.si.ao2.ConfigurationBoitierIcare;

public class MetricsBoitier extends Base {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationBoitierIcare.class);

    private List<Base> objects = new ArrayList<>();
    private int nb;

    public MetricsBoitier() {
        this.setName("cfg");
        this.setClazz("Container");
        this.setEmitted(Instant.now().toString());
        this.setEntity("LOGDM");
        this.setPub(true);
    }

    public List<Base> getObjects() {
        return this.objects;
    }

    public int getNb() {
        return nb;
    }

    public void addObject(Base object) {
        this.objects.add(object);
        nb = this.objects.size();
    }

    public static MetricsBoitier from(List<RawJsonMetric> rawJsonMetrics) {
        MetricsBoitier metricsBoitier = new MetricsBoitier();

        for (RawJsonMetric rawJsonMetric : rawJsonMetrics) {
            switch(rawJsonMetric.name()) {
                case "serving-cell":
                    metricsBoitier.addObject(StatCellServing.from(rawJsonMetric));
                    break;
            }
        }

        return metricsBoitier;
    }

}
