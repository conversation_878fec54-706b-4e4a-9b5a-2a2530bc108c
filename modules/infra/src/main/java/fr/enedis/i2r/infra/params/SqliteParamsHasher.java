package fr.enedis.i2r.infra.params;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.params.BipParameter;

public class SqliteParamsHasher {

    private static final Logger logger = LoggerFactory.getLogger(SqliteParamsHasher.class);

    private static final String hashAlgorithm = "MD5";

    private final Configuration configuration;
    private final List<String> hashableParameters;

    public SqliteParamsHasher(Configuration configuration) {
        this.configuration = configuration;
        this.hashableParameters = Arrays.stream(BipParameter.values())
                .filter(param -> param.watched)
                .map(param -> param.parameterKey)
                .toList();
    }

    public String generateHash() {
        Map<String, String> parameters = this.configuration.parameters();

        List<String> dataRows = new ArrayList<>();
        for (Map.Entry<String, String> entry : parameters.entrySet()) {
            String param = entry.getKey().trim().toLowerCase();
            String value = entry.getValue() == null ? "" : entry.getValue().trim().toLowerCase();

            // Include only "hashable" parameters
            if (hashableParameters.contains(entry.getKey().toLowerCase())) {
                dataRows.add(param + "|" + value);
            }
        }

        Collections.sort(dataRows);
        StringBuilder finalData = new StringBuilder();
        for (String row : dataRows) {
            finalData.append(row).append("\n");
        }

        return computeMD5(finalData.toString());
    }

    private String computeMD5(String input) {
        String hash = "";
        try {
            MessageDigest md = MessageDigest.getInstance(hashAlgorithm);
            md.update(input.getBytes());
            byte[] digest = md.digest();

            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                hexString.append(String.format("%02x", b));
            }

            hash = hexString.toString();
            logger.debug("Hash generated: " + hash);
        } catch (NoSuchAlgorithmException e) {
            logger.error("{} algorithm not found", hashAlgorithm, e);
        }
        return hash;
    }
}
