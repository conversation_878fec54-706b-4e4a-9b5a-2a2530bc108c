package fr.enedis.i2r.infra.rest;

import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.ConfigurationBoitier;
import fr.enedis.i2r.comsi.IpAddress;
import fr.enedis.i2r.comsi.errors.RequestToSiException;
import fr.enedis.i2r.comsi.metrics.RawJsonMetric;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;
import fr.enedis.i2r.comsi.ports.si.SiConfigurationNotifierPort;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.infra.rest.config.StateChange;
import fr.enedis.i2r.infra.rest.si.SiEndpoint;
import fr.enedis.i2r.infra.rest.si.ao2.ConfigurationBoitierIcare;
import fr.enedis.i2r.infra.rest.si.ao2.metrics.MetricsBoitier;
import fr.enedis.i2r.infra.rest.si.error.IComBadRequestException;

public class SiClientHttpAdapter implements SiClientPort, SiConfigurationNotifierPort {

    private static final Logger logger = LoggerFactory.getLogger(SiClientHttpAdapter.class);

    private final ComSiConfiguration comSiConfiguration;
    private final HttpClient httpClient;
    private final ComsiParametersPort parametersPort;
    private final String ads;
    private final String idms;


    public SiClientHttpAdapter(ComSiConfiguration comSiConfiguration, HttpClient httpClient, ComsiParametersPort parametersPort, String ads, String idms) {
        this.comSiConfiguration = comSiConfiguration;
        this.httpClient = httpClient;
        this.parametersPort = parametersPort;
        this.ads = ads;
        this.idms = idms;
    }

    @Override
    public void sendConfigurationBoitier(ConfigurationBoitier config) throws RequestToSiException {
        ConfigurationBoitierIcare configIcare = ConfigurationBoitierIcare.from(config);
        var pathParams = Map.of("idms", config.idms());

        var headers = new SiHeaders(config.ads(), config.configurationHash());

        var siRequest = new SiRequest(SiEndpoint.SendConfig, pathParams, headers, Optional.of(configIcare));

        sendRequestToSi(siRequest);
    }

    @Override
    public void notifyStateChange(BipStatus newStatus) throws RequestToSiException {
        var event = StateChange.from(newStatus);
        var pathParams = Map.of("idms", idms);

        var headers = new SiHeaders(ads, this.parametersPort.getConfigurationHash());
        var request = new SiRequest(SiEndpoint.SendConfig, pathParams, headers, Optional.of(event));

        sendRequestToSi(request);
    }

    private void sendRequestToSi(SiRequest request) throws RequestToSiException {
        /**
         * La méthode semble se répéter, je sais. Cependant, la factoriser avec les messages de logs qui vont avec rendraient
         * le code moins lisible, ce que l'on ne veut pas.
         */
        var datacenterConfiguration = comSiConfiguration.parseDatacenterConfiguration();

        try {
            logger.info("Envoi de la requête au datacenter primaire en cours...");
            this.sendRequestToDatacenter(datacenterConfiguration.primaryIp(), request);

            logger.info("Succès de la requête");
            return;
        } catch (Exception e) {
            logger.warn("Echec de l'envoi", e);
        }

        try {
            logger.info("Envoi de la requête au datacenter secondaire en cours...");
            this.sendRequestToDatacenter(datacenterConfiguration.secondaryIp(), request);

            logger.info("Succès de la requête");
            return;
        } catch (Exception e) {
            logger.warn("Echec de l'envoi", e);
        }

        logger.error("La requête n'a pas pu être envoyée au SI");

        throw new RequestToSiException("échec de l'envoi au SI");
    }

    private void sendRequestToDatacenter(IpAddress datacenterIpAddress, SiRequest request) throws IComBadRequestException {
        try {
            var httpRequest = request.toHttpRequest(datacenterIpAddress);

            httpClient.retryRequest(httpRequest);

        } catch (URISyntaxException e) {
            logger.error("erreur lors de la construction de l'URL", e);
        }
    }

    @Override
    public SiConfigurationNotifierPort getSiConfigurationNotifier() {
        // TODO Cette méthode retourne le service qui se charge des notifications
        // Que ce soit cette classe, ou une autre
        return this;
    }

    @Override
    public void sendMetricsBoitier(List<RawJsonMetric> rawJsonMetrics) throws RequestToSiException {

        // Build object to be posted as body
        MetricsBoitier metricsBoitier = MetricsBoitier.from(rawJsonMetrics);

        var pathParams = Map.of("idms", this.idms);
        var headers = new SiHeaders(this.ads, this.parametersPort.getConfigurationHash());

        // Prepare request
        var siRequest = new SiRequest(SiEndpoint.SendMetrics, pathParams, headers, Optional.of(metricsBoitier));

        // Post request
        sendRequestToSi(siRequest);

    }
}
